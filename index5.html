<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播客播放器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            width: 428px;
            height: 926px;
            overflow: hidden;
            position: relative;
        }

        .player-container {
            width: 100%;
            height: 100%;
            padding: 32px;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 48px;
        }

        .back-button {
            width: 48px;
            height: 48px;
            background: url('./arrow-circle-left-fill.png') no-repeat center;
            background-size: contain;
            border: none;
            cursor: pointer;
            opacity: 0.1;
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
        }

        .menu-button {
            width: 24px;
            height: 24px;
            background: url('./dots-three-outline-vertical-fill.png') no-repeat center;
            background-size: contain;
            border: none;
            cursor: pointer;
            opacity: 0.5;
        }

        .album-cover {
            width: 364px;
            height: 364px;
            border-radius: 24px;
            margin: 0 auto 32px;
            background: url('./podcast-cover.png') no-repeat center;
            background-size: cover;
            position: relative;
        }

        .action-buttons {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 38px;
            margin-bottom: 32px;
            padding: 20px 0;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            width: 266px;
            margin-left: auto;
            margin-right: auto;
        }

        .action-button {
            width: 32px;
            height: 32px;
            border: none;
            cursor: pointer;
            opacity: 0.5;
            background-size: contain;
        }

        .share-button {
            background: url('./share-network-fill.png') no-repeat center;
        }

        .heart-button {
            background: url('./heart-fill.png') no-repeat center;
        }

        .archive-button {
            background: url('./archive-box-fill.png') no-repeat center;
        }

        .track-info {
            text-align: center;
            margin-bottom: 40px;
        }

        .track-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 8px;
        }

        .track-category {
            font-size: 16px;
            color: #1f1f1f;
            opacity: 0.7;
        }

        .progress-section {
            margin-bottom: 40px;
        }

        .time-labels {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
            font-size: 16px;
            color: #1f1f1f;
            opacity: 0.7;
        }

        .progress-bar {
            width: 100%;
            height: 60px;
            background: url('./group-6.png') no-repeat center;
            background-size: contain;
            margin-bottom: 40px;
        }

        .controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
        }

        .control-button {
            width: 32px;
            height: 32px;
            border: none;
            cursor: pointer;
            opacity: 0.7;
            background-size: contain;
        }

        .shuffle-button {
            background: url('./arrows-down-up-fill.png') no-repeat center;
        }

        .rewind-button {
            background: url('./clock-counter-clockwise-fill.png') no-repeat center;
        }

        .play-button {
            width: 80px;
            height: 80px;
            background: url('./group-162.png') no-repeat center;
            background-size: contain;
            border: none;
            cursor: pointer;
        }

        .forward-button {
            background: url('./clock-clockwise-fill.png') no-repeat center;
        }

        .sort-button {
            background: url('./sort-ascending-fill.png') no-repeat center;
        }
    </style>
</head>
<body>
    <div class="player-container">
        <div class="header">
            <button class="back-button"></button>
            <div class="header-title">Now Playing</div>
            <button class="menu-button"></button>
        </div>

        <div class="album-cover"></div>

        <div class="action-buttons">
            <button class="action-button share-button"></button>
            <button class="action-button heart-button"></button>
            <button class="action-button archive-button"></button>
        </div>

        <div class="track-info">
            <div class="track-title">Sunday Vibes - Rift</div>
            <div class="track-category">Entertainment</div>
        </div>

        <div class="progress-section">
            <div class="time-labels">
                <span>07:00</span>
                <span>15:00</span>
            </div>
            <div class="progress-bar"></div>
        </div>

        <div class="controls">
            <button class="control-button shuffle-button"></button>
            <button class="control-button rewind-button"></button>
            <button class="play-button"></button>
            <button class="control-button forward-button"></button>
            <button class="control-button sort-button"></button>
        </div>
    </div>
</body>
</html>
